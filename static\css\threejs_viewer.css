/* 
 * Three.js Model Viewer CSS
 * Contains styles for the 3D model viewer components
 */

/* Model viewer container */
.model-viewer-container {
  position: relative;
  width: 100%;
  height: 300px;
  margin: 0.75rem 0;
  border-radius: 0.5rem;
  overflow: hidden;
  background-color: #f0f0f0;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Controls overlay */
.model-viewer-controls {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 10;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  padding: 0.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: opacity 0.3s ease;
}

/* Hide controls when not hovering over model viewer */
.model-viewer-container:not(:hover) .model-viewer-controls {
  opacity: 0.3;
}

.model-viewer-container:hover .model-viewer-controls {
  opacity: 1;
}

/* Control buttons */
.model-viewer-controls button {
  background: transparent;
  border: none;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.model-viewer-controls button:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

/* Loading spinner */
.loading-spinner {
  width: 24px;
  height: 24px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #3b82f6;
  animation: spinner 0.8s linear infinite;
  margin: 0 auto;
}

@keyframes spinner {
  to {
    transform: rotate(360deg);
  }
}

/* WebGL error message */
.webgl-error {
  padding: 1rem;
  text-align: center;
  background-color: #fee2e2;
  border: 1px solid #fca5a5;
  border-radius: 0.5rem;
  margin: 0.75rem 0;
}

/* Fullscreen mode */
.model-viewer-container:fullscreen {
  width: 100vw;
  height: 100vh;
  padding: 0;
  background-color: black;
}

/* Model tabs when multiple models are displayed */
.model-tabs {
  display: flex;
  border-bottom: 1px solid #e5e7eb;
  background-color: #f9fafb;
  margin-bottom: 0.5rem;
}

.model-tab {
  padding: 0.5rem 1rem;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.model-tab.active {
  border-bottom-color: #3b82f6;
  color: #3b82f6;
  background-color: #eff6ff;
}

.model-tab:hover:not(.active) {
  background-color: #f3f4f6;
  border-bottom-color: #d1d5db;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .model-viewer-container {
    height: 240px;
  }
  
  .model-viewer-controls {
    padding: 0.25rem;
  }
}

/* Placeholder while loading libraries */
.model-placeholder {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
}

/* Loading and error animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.model-loading-indicator,
.model-error-message {
  animation: fadeIn 0.3s ease-out;
} 