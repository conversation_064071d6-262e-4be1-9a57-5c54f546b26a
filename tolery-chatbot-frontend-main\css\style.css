html,
body {
  height: 100%;
  margin: 0;
  font-family: "<PERSON><PERSON><PERSON>", sans-serif;
}
.gap-40 {
  gap: 40px;
}

.px-14 {
  padding-left: 14px;
  padding-right: 14px;
}

.py-14 {
  padding-top: 14px;
  padding-bottom: 14px;
}

.cursor-pointer {
  cursor: pointer;
}

.bg-btn {
  background: linear-gradient(to right, #34c8e8, #4e4af2);
}

.bg-sidebar {
  background: #181818;
}

.bg-gray-dark {
  background: #212121;
}

.bg-gray-light {
  background: #e3e6ea;
}

.box-ai {
  background: #c0c0c0;
  width: 350px;
  padding: 8px;
  border-radius: 0.375rem;
  border-top-left-radius: 1px;
}

.box-user {
  background-color: #0d6efd;
  width: 70%;
  padding: 8px;
  border-radius: 0.375rem;
  border-bottom-right-radius: 1px;
  text-align: start;
  align-self: flex-end;
  color: white;
}

.text-title {
  font-size: 24px;
  font-weight: bold;
  color: white;
}

.text-history {
  font-size: 16px;
  color: white;
}

.text-history:hover {
  color: #0d6efd;
}

.sidebar {
  position: fixed;
  left: -300px;
  top: 0;
  width: 300px;
  height: 100%;
  background: #e3e6ea;
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.5);
  transition: left 0.3s ease;
  z-index: 1000;
}

.sidebar.open {
  left: 0;
}

.hide-scrollbar {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

.input-group textarea.form-control {
  min-height: 38px;
  max-height: 150px;
  line-height: 1.5;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.typing-dots {
  display: inline-block;
  width: 1.4em;
  text-align: left;
}

.typing-dots > span {
  display: inline-block;
  animation-name: blink;
  animation-duration: 1.4s;
  animation-iteration-count: infinite;
  animation-timing-function: linear;
  font-weight: bold;
  font-size: 1.2em;
  color: #666;
}

.typing-dots > span:nth-child(1) {
  animation-delay: 0s;
}
.typing-dots > span:nth-child(2) {
  animation-delay: 0.2s;
}
.typing-dots > span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes blink {
  0% {
    opacity: 0.2;
  }
  20% {
    opacity: 1;
  }
  100% {
    opacity: 0.2;
  }
}
.custom-icon {
  font-size: 18px;
}
