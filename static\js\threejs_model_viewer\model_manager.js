/**
 * ThreeModelManager - Handles multiple 3D model viewers in the chat interface
 * Responsible for creating, maintaining, and cleaning up model viewers
 */

class ThreeModelManager {
  constructor() {
    // Store active model viewers
    this.viewers = new Map();
    
    // Keep track of the next available ID for model container
    this.nextViewerId = 1;
    
    // Track if Three.js libraries are loaded
    this._librariesLoaded = false;
    this._librariesLoading = false;
    this._pendingRenders = [];
  }

  /**
   * Load Three.js libraries if not already loaded
   * @returns {Promise} Resolves when libraries are loaded
   */
  loadLibraries() {
    // Return existing promise if already loading
    if (this._librariesLoading) {
      return this._loadPromise;
    }
    
    // Return resolved promise if already loaded
    if (this._librariesLoaded) {
      return Promise.resolve();
    }
    
    this._librariesLoading = true;
    
    // Create promise for loading libraries
    this._loadPromise = new Promise((resolve, reject) => {
      // Check if Three.js is already loaded
      if (window.THREE) {
        console.log('Three.js is already loaded');
        this._librariesLoaded = true;
        this._librariesLoading = false;
        resolve();
        return;
      }
      
      console.log('Loading Three.js libraries...');
      
      // Function to load script and track progress
      const loadScript = (url) => {
        return new Promise((resolveScript, rejectScript) => {
          const script = document.createElement('script');
          script.src = url;
          script.async = true;
          script.onload = resolveScript;
          script.onerror = () => rejectScript(new Error(`Failed to load script: ${url}`));
          document.head.appendChild(script);
        });
      };
      
      // Load Three.js core and required loaders/controls
      Promise.all([
        loadScript('https://cdn.jsdelivr.net/npm/three@0.158.0/build/three.min.js'),
        loadScript('https://cdn.jsdelivr.net/npm/three@0.158.0/examples/js/loaders/OBJLoader.js'),
        loadScript('https://cdn.jsdelivr.net/npm/three@0.158.0/examples/js/controls/OrbitControls.js')
      ]).then(() => {
        console.log('All Three.js libraries loaded successfully');
        this._librariesLoaded = true;
        this._librariesLoading = false;
        
        // Process any pending renders
        this._processPendingRenders();
        
        resolve();
      }).catch((error) => {
        console.error('Failed to load Three.js libraries:', error);
        this._librariesLoading = false;
        reject(error);
      });
    });
    
    return this._loadPromise;
  }

  /**
   * Process any pending renders that were requested before libraries loaded
   */
  _processPendingRenders() {
    if (this._pendingRenders.length > 0) {
      console.log(`Processing ${this._pendingRenders.length} pending model renders`);
      
      this._pendingRenders.forEach(pending => {
        this.renderModel(pending.objUrl, pending.containerId, pending.options);
      });
      
      this._pendingRenders = [];
    }
  }

  /**
   * Create a container element for a new 3D viewer
   * @param {string} messageId - ID of the message containing the model
   * @returns {string} ID of the created container
   */
  createViewerContainer(messageId) {
    // Create a unique ID for this viewer
    const viewerId = `model-viewer-${this.nextViewerId++}`;
    
    // Create the container element
    const container = document.createElement('div');
    container.id = viewerId;
    container.className = 'model-viewer-container';
    container.style.width = '100%';
    container.style.height = '300px';
    container.style.marginTop = '0.75rem';
    container.style.marginBottom = '0.75rem';
    container.style.borderRadius = '0.5rem';
    container.style.overflow = 'hidden';
    container.style.position = 'relative';
    container.style.backgroundColor = '#f0f0f0';
    container.style.border = '1px solid #e5e7eb';
    
    // Add viewer controls
    const controlsBar = document.createElement('div');
    controlsBar.className = 'model-viewer-controls';
    controlsBar.innerHTML = `
      <div class="absolute top-0 left-0 right-0 bg-black bg-opacity-50 text-white p-2 z-10 flex justify-between items-center">
        <div class="text-sm font-medium">3D Model Viewer</div>
        <div class="space-x-2">
          <button class="model-rotate-btn p-1 hover:bg-white hover:bg-opacity-20 rounded" title="Toggle Auto-Rotate">
            <i class="fas fa-sync-alt"></i>
          </button>
          <button class="model-reset-btn p-1 hover:bg-white hover:bg-opacity-20 rounded" title="Reset Camera">
            <i class="fas fa-home"></i>
          </button>
          <button class="model-fullscreen-btn p-1 hover:bg-white hover:bg-opacity-20 rounded" title="Toggle Fullscreen">
            <i class="fas fa-expand"></i>
          </button>
        </div>
      </div>
    `;
    
    container.appendChild(controlsBar);
    
    // Add a placeholder message while libraries load
    const placeholder = document.createElement('div');
    placeholder.className = 'model-placeholder';
    placeholder.innerHTML = `
      <div class="absolute inset-0 flex items-center justify-center">
        <div class="text-center">
          <div class="loading-spinner mb-2"></div>
          <p class="text-sm text-gray-600">Preparing 3D viewer...</p>
        </div>
      </div>
    `;
    
    container.appendChild(placeholder);
    
    return viewerId;
  }

  /**
   * Render a 3D model in a message
   * @param {string} objUrl - URL to the OBJ file
   * @param {string} containerId - ID of container to render into (optional)
   * @param {object} options - Additional options
   * @returns {Promise} - Resolves when model is rendered
   */
  async renderModel(objUrl, containerId, options = {}) {
    // If no containerId provided, create one
    if (!containerId) {
      containerId = this.createViewerContainer();
    }
    
    // If Three.js libraries aren't loaded yet, queue this render
    if (!this._librariesLoaded) {
      console.log('Three.js libraries not loaded yet, queueing render request');
      this._pendingRenders.push({ objUrl, containerId, options });
      
      // Start loading libraries
      try {
        await this.loadLibraries();
      } catch (error) {
        console.error('Failed to load Three.js libraries:', error);
        this._showError(containerId, 'Failed to load 3D viewer libraries');
        return;
      }
    }
    
    // Check if container exists
    const container = document.getElementById(containerId);
    if (!container) {
      console.error(`Container element with ID "${containerId}" not found`);
      return;
    }
    
    // Create viewer if it doesn't exist
    if (!this.viewers.has(containerId)) {
      try {
        const viewer = new ThreeModelViewer(containerId, options);
        this.viewers.set(containerId, viewer);
        
        // Set up control buttons
        this._setupViewerControls(containerId);
      } catch (error) {
        console.error('Error creating model viewer:', error);
        this._showError(containerId, 'Failed to initialize 3D viewer');
        return;
      }
    }
    
    const viewer = this.viewers.get(containerId);
    
    // Load the model
    try {
      viewer.loadModel(objUrl, options);
    } catch (error) {
      console.error('Error loading model:', error);
      this._showError(containerId, 'Failed to load 3D model');
    }
  }

  /**
   * Set up control buttons for a viewer
   * @param {string} containerId - ID of the viewer container
   */
  _setupViewerControls(containerId) {
    const container = document.getElementById(containerId);
    if (!container) return;
    
    const viewer = this.viewers.get(containerId);
    if (!viewer) return;
    
    // Get control buttons
    const rotateBtn = container.querySelector('.model-rotate-btn');
    const resetBtn = container.querySelector('.model-reset-btn');
    const fullscreenBtn = container.querySelector('.model-fullscreen-btn');
    
    // Toggle auto-rotation
    if (rotateBtn) {
      rotateBtn.addEventListener('click', () => {
        const isRotating = viewer.toggleAutoRotate();
        rotateBtn.innerHTML = isRotating ? 
          '<i class="fas fa-pause"></i>' : 
          '<i class="fas fa-sync-alt"></i>';
      });
    }
    
    // Reset camera view
    if (resetBtn) {
      resetBtn.addEventListener('click', () => {
        viewer.resetCamera();
      });
    }
    
    // Fullscreen mode
    if (fullscreenBtn) {
      fullscreenBtn.addEventListener('click', () => {
        if (!document.fullscreenElement) {
          // Enter fullscreen
          if (container.requestFullscreen) {
            container.requestFullscreen();
          } else if (container.mozRequestFullScreen) {
            container.mozRequestFullScreen();
          } else if (container.webkitRequestFullscreen) {
            container.webkitRequestFullscreen();
          } else if (container.msRequestFullscreen) {
            container.msRequestFullscreen();
          }
          fullscreenBtn.innerHTML = '<i class="fas fa-compress"></i>';
        } else {
          // Exit fullscreen
          if (document.exitFullscreen) {
            document.exitFullscreen();
          } else if (document.mozCancelFullScreen) {
            document.mozCancelFullScreen();
          } else if (document.webkitExitFullscreen) {
            document.webkitExitFullscreen();
          } else if (document.msExitFullscreen) {
            document.msExitFullscreen();
          }
          fullscreenBtn.innerHTML = '<i class="fas fa-expand"></i>';
        }
      });
      
      // Update button icon when fullscreen state changes
      document.addEventListener('fullscreenchange', () => {
        if (document.fullscreenElement === container) {
          fullscreenBtn.innerHTML = '<i class="fas fa-compress"></i>';
        } else {
          fullscreenBtn.innerHTML = '<i class="fas fa-expand"></i>';
        }
      });
    }
  }

  /**
   * Show error message in container
   * @param {string} containerId - ID of the container
   * @param {string} message - Error message to display
   */
  _showError(containerId, message) {
    const container = document.getElementById(containerId);
    if (!container) return;
    
    // Remove placeholder
    const placeholder = container.querySelector('.model-placeholder');
    if (placeholder) {
      placeholder.remove();
    }
    
    // Create error message
    const errorElement = document.createElement('div');
    errorElement.className = 'model-error-message';
    errorElement.innerHTML = `
      <div class="absolute inset-0 flex items-center justify-center bg-red-50 z-10">
        <div class="text-center p-4">
          <p class="text-red-600 mb-2"><i class="fas fa-exclamation-triangle"></i> Error</p>
          <p class="text-sm text-red-500">${message}</p>
        </div>
      </div>
    `;
    
    container.appendChild(errorElement);
  }

  /**
   * Remove a model viewer and clean up resources
   * @param {string} containerId - ID of the container to remove
   */
  removeViewer(containerId) {
    const viewer = this.viewers.get(containerId);
    if (viewer) {
      viewer.dispose();
      this.viewers.delete(containerId);
    }
  }

  /**
   * Remove all viewers and clean up resources
   */
  removeAllViewers() {
    for (const [containerId, viewer] of this.viewers.entries()) {
      viewer.dispose();
    }
    this.viewers.clear();
  }

  /**
   * Get a viewer by container ID
   * @param {string} containerId - ID of the container
   * @returns {ThreeModelViewer} The viewer instance
   */
  getViewer(containerId) {
    return this.viewers.get(containerId);
  }
}

// Create global instance
window.modelManager = new ThreeModelManager(); 