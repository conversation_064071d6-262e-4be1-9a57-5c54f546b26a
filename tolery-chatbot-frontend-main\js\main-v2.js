import * as THREE from "three";
import { OrbitControls } from "OrbitControl";
import { OBJLoader } from "OBJLoader";
import { ViewHelper } from "ViewHelper";

let camera, scene, renderer, width, height, controls;
const raycaster = new THREE.Raycaster();
const mouse = new THREE.Vector2();
let mesh = null;
let container;
let viewHelper; // ViewHelper variable

export function loadObjFile(objPath) {
  if (!container) {
    container = document.getElementById("load-3d");
    if (!container) {
      console.error("Không tìm thấy #load-3d");
      return;
    }
    var positionInfo = container.getBoundingClientRect();
    height = positionInfo.height || 600;
    width = positionInfo.width || 800;

    scene = new THREE.Scene();
    scene.background = new THREE.Color("rgb(230, 225, 225)");
    scene.fog = null;

    // Thêm các nguồn sáng vào scene
    const ambientLight = new THREE.AmbientLight(0xcccccc, 0.4);
    scene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(1, 1, 1);
    scene.add(directionalLight);

    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.5);
    directionalLight2.position.set(-1, 0.5, -1);
    scene.add(directionalLight2);

    camera = new THREE.PerspectiveCamera(45, width / height, 1, 20000);
    camera.position.set(-9, 0, 0);

    renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setPixelRatio(window.devicePixelRatio);
    renderer.setSize(width, height);
    renderer.shadowMap.enabled = true;
    controls = new OrbitControls(camera, renderer.domElement);
    controls.autoRotateSpeed = 3;

    const helperRenderer = new THREE.WebGLRenderer({
      antialias: true,
      alpha: true,
    });
    helperRenderer.setPixelRatio(window.devicePixelRatio);
    helperRenderer.setSize(128, 128);
    helperRenderer.domElement.style.position = "absolute";
    helperRenderer.domElement.style.bottom = "10px";
    helperRenderer.domElement.style.right = "10px";
    helperRenderer.domElement.style.zIndex = "100";

    viewHelper = new ViewHelper(camera, helperRenderer.domElement);

    container.appendChild(renderer.domElement);
    container.appendChild(helperRenderer.domElement);

    onAutoRotate();
    container.addEventListener("dblclick", onMouseDoubleClick, false);

    controls.addEventListener("start", function () {
      controls._wasAutoRotating = controls.autoRotate;
      controls.autoRotate = false;
    });

    controls.addEventListener("end", function () {
      if (controls._wasAutoRotating) {
        controls.autoRotate = true;
      }
    });

    window.addEventListener("resize", onWindowResize);

    function animateHelper() {
      requestAnimationFrame(animateHelper);
      if (viewHelper) {
        viewHelper.render(helperRenderer);
      }
    }

    animate();
    animateHelper();
  }

  const loader = new OBJLoader();

  loader.load(
    objPath,
    function (object) {
      object.traverse(function (child) {
        if (child.isMesh) {
          child.material = new THREE.MeshPhongMaterial({
            color: child.material ? child.material.color : 0xcccccc,
            shininess: 30,
            side: THREE.DoubleSide,
          });
          child.castShadow = true;
          child.receiveShadow = true;
        }
      });

      if (mesh) {
        scene.remove(mesh);
      }

      scene.add(object);
      mesh = object;

      const box = new THREE.Box3().setFromObject(object);
      const size = box.getSize(new THREE.Vector3()).length();
      const center = box.getCenter(new THREE.Vector3());

      camera.near = 0.1;
      camera.far = size * 10;
      camera.updateProjectionMatrix();
      camera.position.copy(center);
      camera.position.x += size / 1;
      camera.position.y += size / 2;
      camera.position.z += size / 1;
      controls.target.copy(center);
      controls.update();
    },
    function (xhr) {
      console.log((xhr.loaded / xhr.total) * 100 + "% loaded");
    },
    function (error) {
      console.error("Error loading OBJ:", error);
    }
  );
}

function onWindowResize() {
  var positionInfo = container.getBoundingClientRect();
  height = positionInfo.height;
  width = positionInfo.width;

  camera.aspect = width / height;
  camera.updateProjectionMatrix();
  renderer.setSize(width, height);
}

function animate() {
  requestAnimationFrame(animate);

  if (typeof TWEEN !== "undefined") {
    TWEEN.update();
  }

  renderer.render(scene, camera);
  controls.update();
}

function onMouseDoubleClick(event) {
  mouse.set(
    (event.clientX / renderer.domElement.clientWidth) * 2 - 1,
    -(event.clientY / renderer.domElement.clientHeight) * 2 + 1
  );
  raycaster.setFromCamera(mouse, camera);

  if (mesh) {
    const intersects = raycaster.intersectObject(mesh, true);

    if (intersects.length > 0) {
      const wasAutoRotating = controls.autoRotate;
      controls.autoRotate = false;

      new TWEEN.Tween(controls.target)
        .to(intersects[0].point, 500)
        .easing(TWEEN.Easing.Cubic.Out)
        .onComplete(function () {
          controls.autoRotate = wasAutoRotating;
        })
        .start();
    }
  }
}

function onStopRotate() {
  controls.autoRotate = false;
}

function onAutoRotate() {
  controls.autoRotate = true;
}

export function clearScene() {
  if (scene && mesh) {
    scene.remove(mesh);
    mesh = null;

    if (camera) {
      camera.position.set(-9, 0, 0);
      camera.lookAt(0, 0, 0);
      camera.updateProjectionMatrix();
    }

    if (controls) {
      controls.target.set(0, 0, 0);
      controls.update();
    }

    if (renderer) {
      renderer.render(scene, camera);
    }

    console.log("3D scene cleared");
  }
}
