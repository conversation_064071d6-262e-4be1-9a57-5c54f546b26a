/**
 * Three.js Model Viewer for displaying OBJ models
 * Provides a reusable interface for rendering 3D models in the chat
 */

class ThreeModelViewer {
  constructor(containerId, options = {}) {
    this.containerId = containerId;
    this.container = document.getElementById(containerId);
    if (!this.container) {
      throw new Error(`Container element with ID "${containerId}" not found.`);
    }

    // Default options
    this.options = Object.assign({
      backgroundColor: 0xf0f0f0,
      defaultModelColor: 0x1e88e5,
      autoRotate: true,
      ambientLightIntensity: 0.5,
      directionalLightIntensity: 0.8
    }, options);

    // Three.js components
    this.scene = null;
    this.camera = null;
    this.renderer = null;
    this.controls = null;
    this.model = null;
    this.animationFrameId = null;
    
    // Store current model info
    this.currentModelUrl = null;
    
    // Initialize the viewer
    this.init();
  }

  /**
   * Initialize the Three.js scene
   */
  init() {
    // Check WebGL support
    if (!this.checkWebGLSupport()) {
      this.showWebGLError();
      return;
    }

    // Create container for the renderer
    const width = this.container.clientWidth;
    const height = this.container.clientHeight || 300; // Default height if container has no height
    
    // Create scene
    this.scene = new THREE.Scene();
    this.scene.background = new THREE.Color(this.options.backgroundColor);

    // Create camera
    this.camera = new THREE.PerspectiveCamera(45, width / height, 0.1, 1000);
    this.camera.position.z = 5;

    // Create renderer
    this.renderer = new THREE.WebGLRenderer({ antialias: true });
    this.renderer.setSize(width, height);
    this.renderer.setPixelRatio(window.devicePixelRatio);
    this.container.appendChild(this.renderer.domElement);

    // Add lighting
    const ambientLight = new THREE.AmbientLight(0xffffff, this.options.ambientLightIntensity);
    this.scene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, this.options.directionalLightIntensity);
    directionalLight.position.set(1, 1, 1);
    this.scene.add(directionalLight);

    // Add opposite direction light for better visibility
    const directionalLight2 = new THREE.DirectionalLight(0xffffff, this.options.directionalLightIntensity * 0.5);
    directionalLight2.position.set(-1, -1, -1);
    this.scene.add(directionalLight2);

    // Add OrbitControls
    this.controls = new THREE.OrbitControls(this.camera, this.renderer.domElement);
    this.controls.enableDamping = true;
    this.controls.dampingFactor = 0.25;
    this.controls.autoRotate = this.options.autoRotate;
    this.controls.autoRotateSpeed = 1.0;
    this.controls.enablePan = true;
    this.controls.enableZoom = true;
    
    // Add event listener for resize
    window.addEventListener('resize', this.onWindowResize.bind(this));
    
    // Start animation loop
    this.animate();
  }

  /**
   * Check if WebGL is supported
   */
  checkWebGLSupport() {
    try {
      const canvas = document.createElement('canvas');
      return !!(
        window.WebGLRenderingContext &&
        (canvas.getContext('webgl') || canvas.getContext('experimental-webgl'))
      );
    } catch (e) {
      return false;
    }
  }

  /**
   * Show WebGL not supported error
   */
  showWebGLError() {
    this.container.innerHTML = `
      <div class="webgl-error p-4 text-center bg-red-50 border border-red-300 rounded">
        <p class="text-red-600 mb-2"><i class="fas fa-exclamation-triangle"></i> WebGL not supported</p>
        <p class="text-sm text-red-500">Your browser does not support WebGL, which is required for 3D visualization.</p>
      </div>
    `;
  }

  /**
   * Handle window resize event
   */
  onWindowResize() {
    if (!this.camera || !this.renderer || !this.container) return;
    
    const width = this.container.clientWidth;
    const height = this.container.clientHeight || 300;
    
    this.camera.aspect = width / height;
    this.camera.updateProjectionMatrix();
    this.renderer.setSize(width, height);
  }

  /**
   * Animation loop
   */
  animate() {
    this.animationFrameId = requestAnimationFrame(this.animate.bind(this));
    
    if (this.controls) {
      this.controls.update();
    }
    
    if (this.renderer && this.scene && this.camera) {
      this.renderer.render(this.scene, this.camera);
    }
  }

  /**
   * Load and display an OBJ model
   * @param {string} url - URL to the OBJ file
   * @param {object} options - Additional options for loading
   */
  loadModel(url, options = {}) {
    if (!url) {
      console.error('No model URL provided');
      return;
    }

    // Store current model URL
    this.currentModelUrl = url;
    
    // Show loading indicator
    this.showLoadingIndicator();

    // Clear previous model if it exists
    if (this.model) {
      this.scene.remove(this.model);
      this.model = null;
    }

    // Load OBJ model
    const loader = new THREE.OBJLoader();
    
    loader.load(
      url,
      // On successful load
      (object) => {
        // Apply material to model
        const material = new THREE.MeshPhongMaterial({
          color: options.modelColor || this.options.defaultModelColor,
          flatShading: true
        });
        
        object.traverse((child) => {
          if (child instanceof THREE.Mesh) {
            child.material = material;
          }
        });
        
        // Add to scene
        this.model = object;
        this.scene.add(this.model);
        
        // Center model
        const box = new THREE.Box3().setFromObject(this.model);
        const center = box.getCenter(new THREE.Vector3());
        const size = box.getSize(new THREE.Vector3());
        
        // Adjust model position to center
        this.model.position.x = -center.x;
        this.model.position.y = -center.y;
        this.model.position.z = -center.z;
        
        // Reset camera position
        const maxDim = Math.max(size.x, size.y, size.z);
        const fov = this.camera.fov * (Math.PI / 180);
        let cameraZ = Math.abs(maxDim / 2 / Math.tan(fov / 2));
        cameraZ *= 1.5; // Add some margin
        
        this.camera.position.z = cameraZ;
        
        // Update controls target
        this.controls.target.set(0, 0, 0);
        this.controls.update();
        
        // Hide loading indicator
        this.hideLoadingIndicator();
      },
      // On progress
      (xhr) => {
        const percent = (xhr.loaded / xhr.total * 100).toFixed(0);
        this.updateLoadingProgress(percent);
      },
      // On error
      (error) => {
        console.error('Error loading model:', error);
        this.hideLoadingIndicator();
        this.showErrorMessage('Failed to load 3D model');
      }
    );
  }

  /**
   * Show loading indicator
   */
  showLoadingIndicator() {
    // Remove any existing loading indicator
    this.hideLoadingIndicator();
    
    // Create and add loading indicator
    const loadingElement = document.createElement('div');
    loadingElement.className = 'model-loading-indicator';
    loadingElement.innerHTML = `
      <div class="absolute inset-0 flex items-center justify-center bg-gray-800 bg-opacity-30 z-10">
        <div class="bg-white p-4 rounded-lg shadow-lg flex flex-col items-center">
          <div class="loading-spinner mb-2"></div>
          <div class="loading-text text-sm">Loading 3D model... <span class="loading-percent">0%</span></div>
        </div>
      </div>
    `;
    
    this.container.style.position = 'relative';
    this.container.appendChild(loadingElement);
  }

  /**
   * Update loading progress
   */
  updateLoadingProgress(percent) {
    const percentElement = this.container.querySelector('.loading-percent');
    if (percentElement) {
      percentElement.textContent = `${percent}%`;
    }
  }

  /**
   * Hide loading indicator
   */
  hideLoadingIndicator() {
    const loadingElement = this.container.querySelector('.model-loading-indicator');
    if (loadingElement) {
      loadingElement.remove();
    }
  }

  /**
   * Show error message
   */
  showErrorMessage(message) {
    const errorElement = document.createElement('div');
    errorElement.className = 'model-error-message';
    errorElement.innerHTML = `
      <div class="absolute inset-0 flex items-center justify-center bg-red-50 bg-opacity-80 z-10">
        <div class="text-center p-4">
          <p class="text-red-600 mb-2"><i class="fas fa-exclamation-triangle"></i> Error</p>
          <p class="text-sm text-red-500">${message}</p>
        </div>
      </div>
    `;
    
    this.container.style.position = 'relative';
    this.container.appendChild(errorElement);
  }

  /**
   * Clean up resources
   */
  dispose() {
    // Stop animation loop
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
    }
    
    // Dispose of Three.js objects
    if (this.controls) {
      this.controls.dispose();
    }
    
    if (this.renderer) {
      this.renderer.dispose();
    }
    
    // Remove event listeners
    window.removeEventListener('resize', this.onWindowResize);
    
    // Clear container
    if (this.container) {
      this.container.innerHTML = '';
    }
  }

  /**
   * Resize the renderer
   */
  resize() {
    this.onWindowResize();
  }

  /**
   * Toggle auto-rotation
   */
  toggleAutoRotate() {
    if (this.controls) {
      this.controls.autoRotate = !this.controls.autoRotate;
      return this.controls.autoRotate;
    }
    return false;
  }

  /**
   * Reset camera position
   */
  resetCamera() {
    if (!this.camera || !this.controls) return;
    
    this.camera.position.set(0, 0, 5);
    this.controls.target.set(0, 0, 0);
    this.controls.update();
  }
}

// Export the class for use in other modules
window.ThreeModelViewer = ThreeModelViewer; 