<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Chat + 3D Viewer</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=An<PERSON>zar+Sans:ital,wght@0,100..1000;1,100..1000&family=Roboto+Slab:wght@100..900&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css"
    />
    <link rel="stylesheet" href="./css/style.css" />
  </head>
  <body>
    <div class="container-fluid h-100">
      <div class="row h-100">
        <div class="col-12 col-md-2 px-0 bg-sidebar">
          <div class="px-14 py-14">
            <p class="mb-0">
              <img
                src="./img/logo.png"
                alt="Logo"
                class="img-fluid object-fit-cover cursor-pointer"
                style="width: 170px"
              />
            </p>
          </div>

          <hr class="border border-secondary" style="height: 1px" />

          <div class="mt-2">
            <div class="px-14">
              <div
                class="d-flex align-items-center gap-2 cursor-pointer"
                onclick="startNewChat()"
              >
                <i class="bi bi-folder-plus text-white custom-icon"></i>
                <p class="mb-0 fw-bold fs-5 text-white">New Chat</p>
              </div>
            </div>
            <hr class="border border-secondary" style="height: 1px" />

            <div class="px-14">
              <div class="d-flex align-items-center gap-2">
                <i class="bi bi-clock-history text-white custom-icon"></i>
                <p class="mb-0 fw-bold fs-5 text-white">Yesterday</p>
              </div>

              <div
                id="sidebar-history"
                class="d-flex flex-column gap-2 overflow-auto hide-scrollbar mt-1"
                style="max-height: 650px"
              ></div>
            </div>
          </div>
        </div>

        <!-- Chatbox -->
        <div
          class="col-12 col-md-4 border-end d-flex flex-column p-3 bg-gray-dark"
        >
          <div class="d-flex align-items-center justify-content-between">
            <div class="border-0 rounded-1 px-2 bg-btn opacity-0">
              <i class="bi bi-list fs-5 text-white"></i>
            </div>
            <div class="text-center text-title">CHAT</div>
            <div class="border-0 rounded-1 px-2 opacity-0">
              <i class="bi bi-cloud-plus text-white custom-icon"></i>
            </div>
          </div>

          <hr class="border border-secondary my-1" style="height: 2px" />

          <div
            class="flex-grow-1 overflow-auto mb-2 mt-2 d-flex flex-column gap-3"
          >
            <!-- Chat từ người dùng: nằm bên phải -->
            <!-- <div class="box-user">
              <strong>You:</strong> These documents help you understanding how
              to use the engine in your own solution.
            </div> -->
            <!-- Chat từ Bot: nằm bên trái -->
            <!-- <div class="box-ai">
              <strong>Bot:</strong> Hello, how can I help?
            </div> -->
          </div>

          <div class="input-group">
            <textarea
              class="form-control"
              placeholder="Type a message..."
              rows="1"
              style="resize: none; overflow: hidden"
            ></textarea>
            <button class="btn btn-primary">
              <i class="bi bi-send"></i>
            </button>
          </div>
        </div>

        <!-- 3D Viewer -->
        <div class="col-12 col-md-6 p-0 h-100" id="load-3d"></div>
      </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.4.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/tween.js/20.0.0/tween.umd.js"></script>

    <script type="importmap">
      {
        "imports": {
          "three": "./js/threeJS/build/three.module.js",
          "OrbitControl": "./js/threeJS/jsm/controls/OrbitControls.js",
          "OBJLoader": "./js/threeJS/jsm/loaders/OBJLoader.js",
          "ViewHelper": "./js/threeJS/jsm/helpers/ViewHelper.js"
        }
      }
    </script>

    <script type="module">
      // Import the loadObjFile and clearScene
      import { loadObjFile, clearScene } from "./js/main-v2.js";
      // let objUrl =
      //   "http://atn-tolery-api.dfm-engineering.com:8124/download/outputs/obj/2025-05-28/box_20250528164507.obj";
      // loadObjFile(objUrl);
      let sessionId = localStorage.getItem("session_id") || "";

      // Make clearScene available globally
      window.clearScene3D = clearScene;

      document.addEventListener("DOMContentLoaded", function () {
        const input = document.querySelector(
          '.form-control[placeholder="Type a message..."]'
        );
        const sendBtn = document.querySelector(".btn.btn-primary");
        const chatBox = document.querySelector(".flex-grow-1");

        function sendMessage() {
          const message = input.value.trim();
          if (!message) return;

          // Xoá thông báo bắt đầu chat nếu có
          const startMsg = chatBox.querySelector(
            ".text-white.text-center.small.mt-2"
          );
          if (startMsg) startMsg.remove();

          // Hiển thị tin nhắn người dùng
          const userMsg = document.createElement("div");
          userMsg.className = "box-user";
          userMsg.innerHTML = `<strong>You:</strong> ${message}`;
          chatBox.appendChild(userMsg);

          // Hiển thị "Bot đang gõ..."
          const botTyping = document.createElement("div");
          botTyping.className = "box-ai typing";
          botTyping.innerHTML = `<strong>Bot:</strong> <span class="typing-dots"><span>.</span><span>.</span><span>.</span></span>`;
          chatBox.appendChild(botTyping);
          chatBox.scrollTop = chatBox.scrollHeight;

          fetch(
            "http://atn-tolery-api.dfm-engineering.com:8124/api-production/chat_to_cad/",
            {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({
                message,
                image_path: "",
                session_id: sessionId,
                part_file_name: "part_file_name",
                export_format: "obj",
                material_choice: "STEEL",
                selected_feature_uuid: "",
              }),
            }
          )
            .then((res) => res.json())
            .then((data) => {
              sessionId = data.session_id;
              localStorage.setItem("session_id", sessionId);

              loadSidebarHistory();

              // Xóa "đang gõ"
              botTyping.remove();

              // Hiển thị câu trả lời của bot
              const botMsg = document.createElement("div");
              botMsg.className = "box-ai";
              botMsg.innerHTML = `<strong>Bot:</strong> ${
                data.chat_response || "No response"
              }`;
              chatBox.appendChild(botMsg);

              if (data.obj_export) {
                loadObjFile(data.obj_export);
              }

              chatBox.scrollTop = chatBox.scrollHeight;
            })
            .catch((err) => {
              botTyping.remove();
              alert("API error: " + err);
            });

          input.value = "";
          input.style.height = "auto";
        }

        // Nhấn nút gửi
        sendBtn.addEventListener("click", sendMessage);

        // Nhấn Enter để gửi (Shift+Enter để xuống dòng)
        input.addEventListener("keydown", (e) => {
          if (e.key === "Enter" && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
          }
        });

        // Tự động tăng chiều cao textarea
        input.addEventListener("input", () => {
          input.style.height = "auto";
          input.style.height = input.scrollHeight + "px";
        });

        // Reset session khi bắt đầu chat mới
        window.addEventListener("resetSessionId", function () {
          sessionId = "";
        });

        // Gọi luôn khi trang load
        loadSidebarHistory();
      });
    </script>

    <script>
      function loadSidebarHistory() {
        const sidebarHistory = document.getElementById("sidebar-history");
        sidebarHistory.innerHTML = "<em>Loading...</em>";

        fetch(
          "http://atn-tolery-api.dfm-engineering.com:8124/api-production/sessions/"
        )
          .then((res) => res.json())
          .then((data) => {
            sidebarHistory.innerHTML = ""; // Clear loading

            if (Array.isArray(data) && data.length > 0) {
              data.forEach((session) => {
                const sessionDiv = document.createElement("div");
                sessionDiv.className =
                  "d-flex justify-content-between align-items-center cursor-pointer py-1 gap-40";

                sessionDiv.innerHTML = `
                  <span class="text-history text-truncate" title="${
                    session.name
                  }">
                    ${session.name || "Unnamed Session"}
                  </span>
                  <div class="delete-icon" data-session-id="${
                    session.session_id
                  }">
                    <i class="bi bi-trash3 text-danger"></i>
                  </div>
                `;

                sidebarHistory.appendChild(sessionDiv);
              });

              // Gắn sự kiện xoá sau khi tạo phần tử
              sidebarHistory
                .querySelectorAll(".delete-icon")
                .forEach((icon) => {
                  icon.addEventListener("click", (e) => {
                    e.stopPropagation(); // Ngăn click lan ra ngoài

                    const sessionId = icon.getAttribute("data-session-id");
                    if (
                      confirm("Are you sure you want to delete this session?")
                    ) {
                      fetch(
                        `http://atn-tolery-api.dfm-engineering.com:8124/api-production/sessions/${sessionId}?deletion_type=hard`,
                        { method: "DELETE" }
                      )
                        .then((res) => {
                          if (res.ok) {
                            loadSidebarHistory(); // Refresh lại danh sách
                          } else {
                            alert("Failed to delete session.");
                          }
                        })
                        .catch(() =>
                          alert("API error while deleting session.")
                        );
                    }
                  });
                });
            } else {
              sidebarHistory.innerHTML = "<em>No sessions found</em>";
            }
          })
          .catch(() => {
            sidebarHistory.innerHTML = "<em>Failed to load session list</em>";
          });
      }
    </script>

    <script>
      function startNewChat() {
        // Xóa session_id để tạo phiên mới
        localStorage.removeItem("session_id");

        // Xóa nội dung chat hiện tại
        const chatBox = document.querySelector(".flex-grow-1");
        if (chatBox) chatBox.innerHTML = "";

        // Tạo thông báo bắt đầu chat mới
        const startMsg = document.createElement("div");
        startMsg.className = "text-white text-center small mt-2";
        startMsg.textContent = "You started a new chat session.";
        chatBox.appendChild(startMsg);

        // Cập nhật phần recent
        const recentDiv = document.getElementById("sidebar-recent");
        if (recentDiv) recentDiv.innerHTML = "<em>No recent session</em>";

        // Clear the 3D scene
        if (window.clearScene3D) {
          window.clearScene3D();
        }

        // Thông báo cho module script reset sessionId
        window.dispatchEvent(new Event("resetSessionId"));

        loadSidebarHistory();
      }
    </script>
  </body>
</html>
