{"objects": {"71": {"x": 1464, "y": 295, "elements": [72, 75, 85, 86, 87, 88], "id": 71, "type": "MeshEditor"}, "72": {"outputLength": 1, "title": "<PERSON><PERSON>", "id": 72, "type": "TitleElement"}, "74": {"value": "<PERSON><PERSON><PERSON>", "id": 74, "type": "StringInput"}, "75": {"inputs": [74], "id": 75, "type": "LabelElement"}, "76": {"value": 0, "id": 76, "type": "NumberInput"}, "77": {"value": 0, "id": 77, "type": "NumberInput"}, "78": {"value": 10, "id": 78, "type": "NumberInput"}, "79": {"value": 0, "id": 79, "type": "NumberInput"}, "80": {"value": 0, "id": 80, "type": "NumberInput"}, "81": {"value": 0, "id": 81, "type": "NumberInput"}, "82": {"value": 100, "id": 82, "type": "NumberInput"}, "83": {"value": 100, "id": 83, "type": "NumberInput"}, "84": {"value": 100, "id": 84, "type": "NumberInput"}, "85": {"inputs": [76, 77, 78], "id": 85, "type": "LabelElement"}, "86": {"inputs": [79, 80, 81], "id": 86, "type": "LabelElement"}, "87": {"inputs": [82, 83, 84], "id": 87, "type": "LabelElement"}, "88": {"inputLength": 1, "links": [138], "id": 88, "type": "LabelElement"}, "105": {"x": 214, "y": 261, "elements": [106, 109], "id": 105, "type": "FileURLEditor"}, "106": {"outputLength": 1, "title": "File URL", "id": 106, "type": "TitleElement"}, "108": {"value": "https://threejs.org/examples/textures/matcaps/matcap-porcelain-white.jpg", "id": 108, "type": "StringInput"}, "109": {"inputs": [108], "id": 109, "type": "Element"}, "113": {"x": 632, "y": 300, "elements": [114, 116, 117, 121, 122, 123], "id": 113, "type": "TextureEditor"}, "114": {"outputLength": 4, "title": "Texture", "id": 114, "type": "TitleElement"}, "116": {"inputLength": 1, "links": [106], "id": 116, "type": "LabelElement"}, "117": {"inputLength": 2, "links": [134], "id": 117, "type": "LabelElement"}, "118": {"options": [{"name": "Repeat Wrapping", "value": 1000}, {"name": "Clamp To <PERSON> Wrapping", "value": 1001}, {"name": "Mirrored Repeat Wrapping", "value": 1002}], "value": "1000", "id": 118, "type": "SelectInput"}, "119": {"options": [{"name": "Repeat Wrapping", "value": 1000}, {"name": "Clamp To <PERSON> Wrapping", "value": 1001}, {"name": "Mirrored Repeat Wrapping", "value": 1002}], "value": "1000", "id": 119, "type": "SelectInput"}, "120": {"value": true, "id": 120, "type": "ToggleInput"}, "121": {"inputs": [118], "id": 121, "type": "LabelElement"}, "122": {"inputs": [119], "id": 122, "type": "LabelElement"}, "123": {"inputs": [120], "id": 123, "type": "LabelElement"}, "133": {"x": 236, "y": 401, "elements": [134], "id": 133, "type": "MatcapUVEditor"}, "134": {"outputLength": 2, "title": "Matcap UV", "id": 134, "type": "TitleElement"}, "137": {"x": 1023, "y": 334, "elements": [138, 140, 141, 142], "id": 137, "type": "BasicMaterialEditor"}, "138": {"outputLength": 1, "title": "Basic Material", "id": 138, "type": "TitleElement"}, "140": {"inputLength": 3, "inputs": [143], "links": [114], "id": 140, "type": "LabelElement"}, "141": {"inputLength": 1, "inputs": [144], "id": 141, "type": "LabelElement"}, "142": {"inputLength": 3, "id": 142, "type": "LabelElement"}, "143": {"value": 16777215, "id": 143, "type": "ColorInput"}, "144": {"min": 0, "max": 1, "value": 1, "id": 144, "type": "SliderInput"}}, "nodes": [71, 105, 113, 133, 137], "id": 2, "type": "<PERSON><PERSON>"}