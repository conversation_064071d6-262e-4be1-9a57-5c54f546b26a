import * as THREE from "three";
import { OrbitControls } from "OrbitControl";
import { OBJLoader } from "OBJLoader";
let camera, scene, renderer, width, height, controls;
const raycaster = new THREE.Raycaster();
const mouse = new THREE.Vector2();
let mesh = null;
let container;

export function loadObjFile(objPath) {
  if (!container) {
    container = document.getElementById("load-3d");
    if (!container) {
      console.error("Không tìm thấy #load-3d");
      return;
    }
    var positionInfo = container.getBoundingClientRect();
    height = positionInfo.height || 600;
    width = positionInfo.width || 800;

    scene = new THREE.Scene();
    scene.background = new THREE.Color("rgb(230, 225, 225)");
    scene.fog = null;

    // Thêm các nguồn sáng vào scene
    const ambientLight = new THREE.AmbientLight(0xcccccc, 0.4);
    scene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(1, 1, 1);
    scene.add(directionalLight);

    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.5);
    directionalLight2.position.set(-1, 0.5, -1);
    scene.add(directionalLight2);

    camera = new THREE.PerspectiveCamera(45, width / height, 1, 20000);
    camera.position.set(-9, 0, 0);

    renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setPixelRatio(window.devicePixelRatio);
    renderer.setSize(width, height);
    renderer.shadowMap.enabled = true;
    controls = new OrbitControls(camera, renderer.domElement);
    controls.autoRotateSpeed = 5;

    container.appendChild(renderer.domElement);
    onAutoRotate();
    container.addEventListener("dblclick", onMouseDoubleClick, false);

    // Thêm sự kiện để chỉ tạm dừng khi đang tương tác
    controls.addEventListener("start", function () {
      // Lưu trạng thái tự xoay hiện tại khi bắt đầu tương tác
      controls._wasAutoRotating = controls.autoRotate;
      controls.autoRotate = false;
    });

    controls.addEventListener("end", function () {
      // Khôi phục trạng thái tự xoay sau khi kết thúc tương tác
      if (controls._wasAutoRotating) {
        controls.autoRotate = true;
      }
    });

    window.addEventListener("resize", onWindowResize);
    animate();
  }

  const loader = new OBJLoader();

  loader.load(objPath, function (object) {
    // Áp dụng vật liệu có khả năng phản xạ ánh sáng cho mỗi mesh trong model
    object.traverse(function (child) {
      if (child.isMesh) {
        // Sử dụng MeshPhongMaterial thay vì MeshNormalMaterial
        // MeshPhongMaterial phản ứng với ánh sáng tốt hơn
        child.material = new THREE.MeshPhongMaterial({
          color: child.material ? child.material.color : 0xcccccc,
          shininess: 30,
          side: THREE.DoubleSide,
        });

        // Bật nhận bóng và tạo bóng
        child.castShadow = true;
        child.receiveShadow = true;
      }
    });

    scene.add(object);
    mesh = object; // Gán object vừa load vào biến mesh

    // 👉 Auto-fit camera
    const box = new THREE.Box3().setFromObject(object);
    const size = box.getSize(new THREE.Vector3()).length();
    const center = box.getCenter(new THREE.Vector3());
    camera.near = 0.1;
    camera.far = size * 10;
    camera.updateProjectionMatrix();
    camera.position.copy(center);
    camera.position.x += size / 1;
    camera.position.y += size / 2;
    camera.position.z += size / 1;
    controls.target.copy(center);
    controls.update();
  });
}

function onWindowResize() {
  var positionInfo = container.getBoundingClientRect();
  height = positionInfo.height;
  width = positionInfo.width;

  camera.aspect = width / height;
  camera.updateProjectionMatrix();
  renderer.setSize(width, height);
}

function animate() {
  requestAnimationFrame(animate);

  // Cập nhật TWEEN nếu có
  if (typeof TWEEN !== "undefined") {
    TWEEN.update();
  }

  renderer.render(scene, camera);
  controls.update();
}

// Sửa lại hàm onMouseDoubleClick để giữ trạng thái autoRotate
function onMouseDoubleClick(event) {
  mouse.set(
    (event.clientX / renderer.domElement.clientWidth) * 2 - 1,
    -(event.clientY / renderer.domElement.clientHeight) * 2 + 1
  );
  raycaster.setFromCamera(mouse, camera);
  const intersects = raycaster.intersectObject(mesh, true);

  if (intersects.length > 0) {
    // Lưu trạng thái tự động xoay
    const wasAutoRotating = controls.autoRotate;

    // Tạm thời tắt xoay trong khi di chuyển camera
    controls.autoRotate = false;

    new TWEEN.Tween(controls.target)
      .to(intersects[0].point, 500)
      .easing(TWEEN.Easing.Cubic.Out)
      .onComplete(function () {
        // Khôi phục trạng thái tự động xoay sau khi di chuyển xong
        controls.autoRotate = wasAutoRotating;
      })
      .start();
  }
}

// Giữ lại hàm onStopRotate và onAutoRotate để sử dụng cho các trường hợp khác
function onStopRotate() {
  controls.autoRotate = false;
}

function onAutoRotate() {
  controls.autoRotate = true;
}
